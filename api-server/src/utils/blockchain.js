const { ethers } = require('ethers');
const logger = require('./logger');

// Contract ABI (simplified for the functions we need)
const CONTRACT_ABI = [
  "function mint(address to, uint256 amount) external",
  "function burn(uint256 amount) external",
  "function burnFrom(address account, uint256 amount) external",
  "function balanceOf(address account) view returns (uint256)",
  "function totalSupply() view returns (uint256)",
  "function transfer(address to, uint256 amount) returns (bool)",
  "function owner() view returns (address)",
  "event Transfer(address indexed from, address indexed to, uint256 value)"
];

class BlockchainService {
  constructor() {
    this.provider = null;
    this.wallet = null;
    this.contract = null;
    this.initialize();
  }

  initialize() {
    try {
      // Initialize provider
      this.provider = new ethers.JsonRpcProvider(process.env.RPC_URL);

      // Initialize wallet
      if (!process.env.PRIVATE_KEY) {
        throw new Error('PRIVATE_KEY not found in environment variables');
      }
      this.wallet = new ethers.Wallet(process.env.PRIVATE_KEY, this.provider);

      // Initialize contract
      this.contract = new ethers.Contract(
        process.env.CONTRACT_ADDRESS,
        CONTRACT_ABI,
        this.wallet
      );

      logger.info('Blockchain service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize blockchain service:', error);
      throw error;
    }
  }

  async getBalance(address) {
    try {
      const balance = await this.contract.balanceOf(address);
      return ethers.formatEther(balance);
    } catch (error) {
      logger.error(`Error getting balance for ${address}:`, error);
      throw error;
    }
  }

  async getTotalSupply() {
    try {
      const totalSupply = await this.contract.totalSupply();
      return ethers.formatEther(totalSupply);
    } catch (error) {
      logger.error('Error getting total supply:', error);
      throw error;
    }
  }

  async mintTokens(toAddress, amount) {
    try {
      logger.info(`Minting ${amount} tokens to ${toAddress}`);

      const amountWei = ethers.parseEther(amount.toString());
      const tx = await this.contract.mint(toAddress, amountWei);

      logger.info(`Mint transaction sent: ${tx.hash}`);

      const receipt = await tx.wait();
      logger.info(`Mint transaction confirmed: ${receipt.hash}`);

      return {
        success: true,
        transactionHash: receipt.hash,
        blockNumber: receipt.blockNumber,
        gasUsed: receipt.gasUsed.toString()
      };
    } catch (error) {
      logger.error(`Error minting tokens to ${toAddress}:`, error);
      throw error;
    }
  }

  async burnTokens(amount) {
    try {
      logger.info(`Burning ${amount} tokens`);

      const amountWei = ethers.parseEther(amount.toString());
      const tx = await this.contract.burn(amountWei);

      logger.info(`Burn transaction sent: ${tx.hash}`);

      const receipt = await tx.wait();
      logger.info(`Burn transaction confirmed: ${receipt.hash}`);

      return {
        success: true,
        transactionHash: receipt.hash,
        blockNumber: receipt.blockNumber,
        gasUsed: receipt.gasUsed.toString()
      };
    } catch (error) {
      logger.error(`Error burning tokens:`, error);
      throw error;
    }
  }

  async burnFromAddress(fromAddress, amount) {
    try {
      logger.info(`Burning ${amount} tokens from ${fromAddress}`);

      const amountWei = ethers.parseEther(amount.toString());
      const tx = await this.contract.burnFrom(fromAddress, amountWei);

      logger.info(`BurnFrom transaction sent: ${tx.hash}`);

      const receipt = await tx.wait();
      logger.info(`BurnFrom transaction confirmed: ${receipt.hash}`);

      return {
        success: true,
        transactionHash: receipt.hash,
        blockNumber: receipt.blockNumber,
        gasUsed: receipt.gasUsed.toString()
      };
    } catch (error) {
      logger.error(`Error burning tokens from ${fromAddress}:`, error);
      throw error;
    }
  }

  async getTransactionHistory(address, fromBlock = -10000) {
    try {
      const filter = this.contract.filters.Transfer();
      const events = await this.contract.queryFilter(filter, fromBlock);

      const userTransactions = events.filter(event =>
        event.args[0].toLowerCase() === address.toLowerCase() ||
        event.args[1].toLowerCase() === address.toLowerCase()
      );

      const transactions = await Promise.all(
        userTransactions.map(async (event) => {
          const block = await event.getBlock();
          return {
            hash: event.transactionHash,
            from: event.args[0],
            to: event.args[1],
            amount: ethers.formatEther(event.args[2]),
            timestamp: block.timestamp * 1000,
            blockNumber: event.blockNumber
          };
        })
      );

      return transactions.sort((a, b) => b.timestamp - a.timestamp);
    } catch (error) {
      logger.error(`Error getting transaction history for ${address}:`, error);
      throw error;
    }
  }

  async validateAddress(address) {
    return ethers.isAddress(address);
  }

  async getNetworkInfo() {
    try {
      const network = await this.provider.getNetwork();
      const blockNumber = await this.provider.getBlockNumber();

      return {
        chainId: Number(network.chainId),
        name: network.name,
        blockNumber: blockNumber
      };
    } catch (error) {
      logger.error('Error getting network info:', error);
      throw error;
    }
  }
}

// Create and export singleton instance
const blockchainService = new BlockchainService();
module.exports = blockchainService;
