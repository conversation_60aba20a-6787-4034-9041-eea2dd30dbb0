require("@nomicfoundation/hardhat-toolbox");
require("dotenv").config();

console.log("🔧 SEPOLIA_RPC_URL:", process.env.SEPOLIA_RPC_URL);
console.log("🔧 PRIVATE_KEY:", process.env.PRIVATE_KEY?.slice(0, 10) + "... (" + (process.env.PRIVATE_KEY?.length || 0) + " chars)");


module.exports = {
  solidity: "0.8.28",
  networks: {
    sepolia: {
      url: process.env.SEPOLIA_RPC_URL,
      accounts: [process.env.PRIVATE_KEY],
      chainId: ********
    },
    localhost: {
      url: "http://127.0.0.1:8545",
      chainId: 31337
    },
    ethermint: {
      url: "http://127.0.0.1:8545",
      accounts: [process.env.PRIVATE_KEY],
      chainId: 9000
    }
  },
};
