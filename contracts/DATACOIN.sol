// SPDX-License-Identifier: MIT
pragma solidity ^0.8.28;

import "@openzeppelin/contracts/token/ERC20/extensions/ERC20Burnable.sol";
import "@openzeppelin/contracts/access/Ownable.sol";

contract DATACOIN is ERC20Burnable, Ownable {
    mapping(address => mapping(address => uint256)) private _lastTransferAmount;

    constructor(address initialOwner) ERC20("DATACOIN", "DTC") Ownable(initialOwner) {
        // Khởi tạo token rỗng, chỉ mint khi owner gọi
    }

    function mint(address to, uint256 amount) external onlyOwner {
        _mint(to, amount);
    }

    function transferWithTracking(address to, uint256 amount) public returns (bool) {
        _lastTransferAmount[to][msg.sender] = amount;
        return transfer(to, amount);
    }

    function reverseTransfer(address from, address to) external onlyOwner {
        uint256 amount = _lastTransferAmount[to][from];
        require(amount > 0, "No record");
        require(balanceOf(to) >= amount, "Insufficient balance");

        _transfer(to, from, amount);
        _lastTransferAmount[to][from] = 0;
    }

    function getLastTransferAmount(address to, address from) external view returns (uint256) {
        return _lastTransferAmount[to][from];
    }
}
