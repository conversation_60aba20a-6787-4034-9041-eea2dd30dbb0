const { buildModule } = require("@nomicfoundation/hardhat-ignition/modules");

/**
 * Simple DATACOIN deployment module
 * 
 * Usage examples:
 * 
 * 1. Deploy with default parameters:
 *    npx hardhat ignition deploy ignition/modules/DatacoinSimple.js --network ethermint
 * 
 * 2. Deploy with custom owner:
 *    npx hardhat ignition deploy ignition/modules/DatacoinSimple.js --network ethermint --parameters '{"owner": "******************************************"}'
 * 
 * 3. Deploy with custom parameters file:
 *    Create ignition/parameters/ethermint.json with:
 *    {
 *      "DatacoinSimpleModule": {
 *        "owner": "******************************************",
 *        "mintAmount": "5000000000000000000000000"
 *      }
 *    }
 *    Then run: npx hardhat ignition deploy ignition/modules/DatacoinSimple.js --network ethermint
 */

module.exports = buildModule("DatacoinSimpleModule", (m) => {
  // Parameters with sensible defaults
  const owner = m.getParameter("owner", "******************************************");
  const mintAmount = m.getParameter("mintAmount", "2000000000000000000000000"); // 2M tokens in wei
  const shouldMint = m.getParameter("shouldMint", true);
  
  console.log(`Deploying DATACOIN with owner: ${owner}`);
  console.log(`Mint amount: ${mintAmount} wei`);
  console.log(`Should mint: ${shouldMint}`);
  
  // Deploy DATACOIN contract
  const datacoin = m.contract("DATACOIN", [owner], {
    gasLimit: 2000000,
  });

  // Conditionally mint tokens
  if (shouldMint) {
    m.call(datacoin, "mint", [owner, mintAmount], {
      id: "mint_tokens",
      gasLimit: 200000,
    });
  }

  return { 
    datacoin,
    owner,
    mintAmount,
    shouldMint
  };
});
