const { buildModule } = require("@nomicfoundation/hardhat-ignition/modules");

module.exports = buildModule("DatacoinWithMintModule", (m) => {
  // Parameters with default values
  const owner = m.getParameter("owner", "0xBe07083dbf7073B2d2C550c97EBC016dcC4B74aD");
  const initialSupply = m.getParameter("initialSupply", "1000000000000000000000000"); // 1 million tokens in wei

  // Deploy DATACOIN contract
  const datacoin = m.contract("DATACOIN", [owner], {
    gasLimit: 2000000,
  });

  // Mint initial supply to owner (using wei directly)
  m.call(datacoin, "mint", [owner, initialSupply], {
    id: "mint_initial_supply",
    gasLimit: 200000,
  });

  // Optional: Set up additional addresses with tokens
  const testAddress = m.getParameter("testAddress", "0x742d35cc6634c0532925a3b8d4c9db96c4b4d8b6");
  const testAmount = m.getParameter("testAmount", "10000000000000000000000"); // 10k tokens in wei

  m.call(datacoin, "mint", [testAddress, testAmount], {
    id: "mint_test_tokens",
    gasLimit: 200000,
  });

  return {
    datacoin,
    owner,
    initialSupply,
    testAddress,
    testAmount
  };
});
