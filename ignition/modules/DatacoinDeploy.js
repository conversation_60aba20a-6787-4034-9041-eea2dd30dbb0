const { buildModule } = require("@nomicfoundation/hardhat-ignition/modules");

module.exports = buildModule("DatacoinDeployModule", (m) => {
  // Get owner address from parameters or use default
  const owner = m.getParameter("owner", "0xBe07083dbf7073B2d2C550c97EBC016dcC4B74aD");
  
  // Deploy DATACOIN contract with owner as constructor parameter
  const datacoin = m.contract("DATACOIN", [owner], {
    gasLimit: 2000000,
  });

  // Return the deployed contract for use in other modules or scripts
  return { datacoin };
});
