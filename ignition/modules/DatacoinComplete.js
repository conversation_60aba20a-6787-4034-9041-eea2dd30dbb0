const { buildModule } = require("@nomicfoundation/hardhat-ignition/modules");

module.exports = buildModule("DatacoinCompleteModule", (m) => {
  // Configuration parameters
  const owner = m.getParameter("owner", "0xBe07083dbf7073B2d2C550c97EBC016dcC4B74aD");
  const initialSupply = m.getParameter("initialSupply", "1000000");
  const teamAddress = m.getParameter("teamAddress", "******************************************");
  const marketingAddress = m.getParameter("marketingAddress", "0x8ba1f109551bd432803012645hac136c0c8326b");
  
  // Deploy DATACOIN contract
  const datacoin = m.contract("DATACOIN", [owner], {
    gasLimit: 2000000,
  });

  // Mint initial supply to owner
  const ownerSupply = m.staticCall(datacoin, "parseEther", [initialSupply]);
  m.call(datacoin, "mint", [owner, ownerSupply], {
    id: "mint_owner_supply",
    gasLimit: 200000,
  });

  // Mint team allocation (10% of initial supply)
  const teamSupply = m.staticCall(datacoin, "parseEther", [(parseInt(initialSupply) * 0.1).toString()]);
  m.call(datacoin, "mint", [teamAddress, teamSupply], {
    id: "mint_team_supply",
    gasLimit: 200000,
  });

  // Mint marketing allocation (5% of initial supply)
  const marketingSupply = m.staticCall(datacoin, "parseEther", [(parseInt(initialSupply) * 0.05).toString()]);
  m.call(datacoin, "mint", [marketingAddress, marketingSupply], {
    id: "mint_marketing_supply",
    gasLimit: 200000,
  });

  // Setup allowances for team and marketing
  const allowanceAmount = m.staticCall(datacoin, "parseEther", ["50000"]); // 50k tokens allowance
  
  m.call(datacoin, "approve", [teamAddress, allowanceAmount], {
    id: "approve_team_allowance",
    gasLimit: 100000,
    from: owner,
  });

  m.call(datacoin, "approve", [marketingAddress, allowanceAmount], {
    id: "approve_marketing_allowance", 
    gasLimit: 100000,
    from: owner,
  });

  return { 
    datacoin,
    owner,
    teamAddress,
    marketingAddress,
    initialSupply,
    ownerSupply,
    teamSupply,
    marketingSupply
  };
});
