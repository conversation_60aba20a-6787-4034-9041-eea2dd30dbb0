async function main() {
  console.log("🪙 Minting DTC to specific account");
  console.log("=".repeat(50));
  
  const contractAddress = "******************************************";
  const targetAccount = "******************************************";
  const mintAmount = "10000"; // 10,000 DTC
  
  const [deployer] = await ethers.getSigners();
  
  console.log("📋 Mint Details:");
  console.log("- Contract:", contractAddress);
  console.log("- Target Account:", targetAccount);
  console.log("- Amount:", mintAmount, "DTC");
  console.log("- Minter:", deployer.address);
  
  // Kết nối với contract
  const DATACOIN = await ethers.getContractFactory("DATACOIN");
  const token = DATACOIN.attach(contractAddress);
  
  try {
    // <PERSON><PERSON><PERSON> tra balance trước mint
    const balanceBefore = await token.balanceOf(targetAccount);
    console.log("- Balance Before:", ethers.formatEther(balanceBefore), "DTC");
    
    // Mint DTC
    console.log("\n🚀 Minting DTC...");
    const mintAmountWei = ethers.parseEther(mintAmount);
    
    const tx = await token.mint(targetAccount, mintAmountWei, {
      gasLimit: 200000
    });
    
    console.log("- Transaction Hash:", tx.hash);
    console.log("- Waiting for confirmation...");
    
    const receipt = await tx.wait();
    console.log("- ✅ Confirmed in Block:", receipt.blockNumber);
    console.log("- Gas Used:", receipt.gasUsed.toString());
    
    // Kiểm tra balance sau mint
    const balanceAfter = await token.balanceOf(targetAccount);
    console.log("- Balance After:", ethers.formatEther(balanceAfter), "DTC");
    
    const totalSupply = await token.totalSupply();
    console.log("- Total Supply:", ethers.formatEther(totalSupply), "DTC");
    
    console.log("\n🎉 Mint successful!");
    console.log(`Account ${targetAccount} now has ${ethers.formatEther(balanceAfter)} DTC`);
    
    // Explorer links
    console.log("\n🔍 Explorer Links:");
    console.log(`- Transaction: http://localhost:8000/tx/${tx.hash}`);
    console.log(`- Account: http://localhost:8000/address/${targetAccount}`);
    console.log(`- Contract: http://localhost:8000/address/${contractAddress}`);
    
  } catch (error) {
    console.error("❌ Mint failed:", error.message);
    throw error;
  }
}

main().catch((error) => {
  console.error("💥 Script failed:", error);
  process.exitCode = 1;
});
