async function main() {
  console.log("🧪 Testing Ethermint connection and contract interaction...");
  
  const [deployer] = await ethers.getSigners();
  
  // Contract address từ deployment vừa rồi
  const contractAddress = "******************************************";
  
  console.log("📋 Test Details:");
  console.log("- Network:", hre.network.name);
  console.log("- Account:", deployer.address);
  console.log("- Contract Address:", contractAddress);
  console.log("- Chain ID:", (await ethers.provider.getNetwork()).chainId);
  
  // <PERSON><PERSON><PERSON> tra balance của account
  const balance = await ethers.provider.getBalance(deployer.address);
  console.log("- Account Balance:", ethers.formatEther(balance), "ETH");
  
  // Kết nối với contract đã deploy
  const DATACOIN = await ethers.getContractFactory("DATACOIN");
  const token = DATACOIN.attach(contractAddress);
  
  console.log("\n🔍 Testing contract functions:");
  
  try {
    // Test 1: <PERSON><PERSON><PERSON> thông tin cơ bản của token
    const name = await token.name();
    const symbol = await token.symbol();
    const decimals = await token.decimals();
    const totalSupply = await token.totalSupply();
    
    console.log("✅ Token Info:");
    console.log("  - Name:", name);
    console.log("  - Symbol:", symbol);
    console.log("  - Decimals:", decimals.toString());
    console.log("  - Total Supply:", ethers.formatEther(totalSupply));
    
    // Test 2: Kiểm tra balance của deployer
    const deployerBalance = await token.balanceOf(deployer.address);
    console.log("  - Deployer Balance:", ethers.formatEther(deployerBalance));
    
    // Test 3: Thử transfer một lượng nhỏ token (nếu có balance)
    if (deployerBalance > 0) {
      console.log("\n💸 Testing token transfer...");
      const transferAmount = ethers.parseEther("1"); // 1 token
      
      // Tạo một địa chỉ test để transfer
      const testAddress = "******************************************";
      
      const tx = await token.transfer(testAddress, transferAmount, {
        gasLimit: 100000
      });
      
      console.log("  - Transfer TX Hash:", tx.hash);
      await tx.wait();
      
      const newBalance = await token.balanceOf(testAddress);
      console.log("  - Test Address Balance:", ethers.formatEther(newBalance));
      console.log("✅ Transfer successful!");
    }
    
    // Test 4: Kiểm tra block number hiện tại
    const blockNumber = await ethers.provider.getBlockNumber();
    console.log("\n📦 Current Block Number:", blockNumber);
    
    // Test 5: Kiểm tra gas price
    const gasPrice = await ethers.provider.getFeeData();
    console.log("⛽ Gas Price:", ethers.formatUnits(gasPrice.gasPrice, "gwei"), "gwei");
    
    console.log("\n🎉 All tests passed! Ethermint connection is working properly.");
    
  } catch (error) {
    console.error("❌ Test failed:", error.message);
    throw error;
  }
}

main().catch((error) => {
  console.error("💥 Script failed:", error);
  process.exitCode = 1;
});
