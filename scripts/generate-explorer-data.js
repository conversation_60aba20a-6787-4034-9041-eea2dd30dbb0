async function main() {
  console.log("🌐 Generating Data for Ethermint Explorer");
  console.log("=".repeat(50));
  
  const contractAddress = "******************************************";
  const [deployer] = await ethers.getSigners();
  
  console.log("📋 Info:");
  console.log("- Contract:", contractAddress);
  console.log("- Deployer:", deployer.address);
  console.log("- Network:", hre.network.name);
  
  const DATACOIN = await ethers.getContractFactory("DATACOIN");
  const token = DATACOIN.attach(contractAddress);
  
  const symbol = await token.symbol();
  const balance = await token.balanceOf(deployer.address);
  console.log("- Balance:", ethers.formatEther(balance), symbol);
  
  // Danh sách địa chỉ để tạo transactions
  const addresses = [
    "******************************************",
    "******************************************", 
    "******************************************",
    "******************************************",
    "******************************************",
    "******************************************"
  ];
  
  console.log("\n🚀 Creating Multiple Transactions:");
  console.log("-".repeat(50));
  
  // Batch 1: Small transfers
  console.log("\n📦 Batch 1: Small Transfers");
  const smallAmounts = ["10", "15", "20", "25", "30", "35"];
  
  for (let i = 0; i < addresses.length; i++) {
    const recipient = addresses[i];
    const amount = smallAmounts[i];
    
    try {
      const transferAmount = ethers.parseEther(amount);
      const tx = await token.transfer(recipient, transferAmount, {
        gasLimit: 100000
      });
      
      console.log(`   ${i + 1}. ${amount} DTC → ${recipient.slice(0, 10)}... | TX: ${tx.hash.slice(0, 10)}...`);
      await tx.wait();
      
    } catch (error) {
      console.log(`   ${i + 1}. ❌ Failed: ${error.message.slice(0, 50)}...`);
    }
    
    // Small delay
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  // Batch 2: Medium transfers
  console.log("\n📦 Batch 2: Medium Transfers");
  const mediumAmounts = ["50", "75", "100", "125", "150", "175"];
  
  for (let i = 0; i < addresses.length; i++) {
    const recipient = addresses[i];
    const amount = mediumAmounts[i];
    
    try {
      const transferAmount = ethers.parseEther(amount);
      const tx = await token.transfer(recipient, transferAmount, {
        gasLimit: 100000
      });
      
      console.log(`   ${i + 1}. ${amount} DTC → ${recipient.slice(0, 10)}... | TX: ${tx.hash.slice(0, 10)}...`);
      await tx.wait();
      
    } catch (error) {
      console.log(`   ${i + 1}. ❌ Failed: ${error.message.slice(0, 50)}...`);
    }
    
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  // Batch 3: Large transfers
  console.log("\n📦 Batch 3: Large Transfers");
  const largeAmounts = ["200", "300", "400", "500", "600", "700"];
  
  for (let i = 0; i < addresses.length; i++) {
    const recipient = addresses[i];
    const amount = largeAmounts[i];
    
    try {
      const transferAmount = ethers.parseEther(amount);
      const tx = await token.transfer(recipient, transferAmount, {
        gasLimit: 100000
      });
      
      console.log(`   ${i + 1}. ${amount} DTC → ${recipient.slice(0, 10)}... | TX: ${tx.hash.slice(0, 10)}...`);
      await tx.wait();
      
    } catch (error) {
      console.log(`   ${i + 1}. ❌ Failed: ${error.message.slice(0, 50)}...`);
    }
    
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  // Batch 4: Random transfers
  console.log("\n📦 Batch 4: Random Transfers");
  
  for (let i = 0; i < 10; i++) {
    const randomRecipient = addresses[Math.floor(Math.random() * addresses.length)];
    const randomAmount = (Math.random() * 100 + 10).toFixed(2); // 10-110 DTC
    
    try {
      const transferAmount = ethers.parseEther(randomAmount);
      const tx = await token.transfer(randomRecipient, transferAmount, {
        gasLimit: 100000
      });
      
      console.log(`   ${i + 1}. ${randomAmount} DTC → ${randomRecipient.slice(0, 10)}... | TX: ${tx.hash.slice(0, 10)}...`);
      await tx.wait();
      
    } catch (error) {
      console.log(`   ${i + 1}. ❌ Failed: ${error.message.slice(0, 50)}...`);
    }
    
    await new Promise(resolve => setTimeout(resolve, 300));
  }
  
  // Batch 5: Approve transactions
  console.log("\n📦 Batch 5: Approve Transactions");
  
  for (let i = 0; i < addresses.length; i++) {
    const spender = addresses[i];
    const approveAmount = ethers.parseEther((i + 1) * 50 + ""); // 50, 100, 150, etc.
    
    try {
      const tx = await token.approve(spender, approveAmount, {
        gasLimit: 100000
      });
      
      console.log(`   ${i + 1}. Approve ${(i + 1) * 50} DTC for ${spender.slice(0, 10)}... | TX: ${tx.hash.slice(0, 10)}...`);
      await tx.wait();
      
    } catch (error) {
      console.log(`   ${i + 1}. ❌ Failed: ${error.message.slice(0, 50)}...`);
    }
    
    await new Promise(resolve => setTimeout(resolve, 300));
  }
  
  // Final statistics
  console.log("\n📊 Final Statistics:");
  console.log("-".repeat(50));
  
  const finalBalance = await token.balanceOf(deployer.address);
  const totalSupply = await token.totalSupply();
  const blockNumber = await ethers.provider.getBlockNumber();
  
  console.log(`Deployer Balance: ${ethers.formatEther(finalBalance)} ${symbol}`);
  console.log(`Total Supply: ${ethers.formatEther(totalSupply)} ${symbol}`);
  console.log(`Current Block: ${blockNumber}`);
  
  console.log(`\nAddress Balances:`);
  for (let i = 0; i < addresses.length; i++) {
    const balance = await token.balanceOf(addresses[i]);
    console.log(`  ${addresses[i]}: ${ethers.formatEther(balance)} ${symbol}`);
  }
  
  console.log("\n🎉 Explorer data generation completed!");
  console.log("💡 You should now have plenty of transactions to view on your Ethermint explorer!");
  console.log(`🌐 Explorer: http://localhost:8545`);
  console.log(`📄 Contract: ${contractAddress}`);
  
  // Summary of what was created
  console.log("\n📋 Summary of Created Transactions:");
  console.log("- Small transfers: 6 transactions");
  console.log("- Medium transfers: 6 transactions"); 
  console.log("- Large transfers: 6 transactions");
  console.log("- Random transfers: 10 transactions");
  console.log("- Approve transactions: 6 transactions");
  console.log("- Total: ~34 transactions");
}

main().catch((error) => {
  console.error("💥 Failed:", error);
  process.exitCode = 1;
});
