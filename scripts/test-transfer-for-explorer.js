async function main() {
  console.log("💸 Testing DTC Transfer for Ethermint Explorer");
  console.log("=".repeat(60));

  // Sử dụng contract có nhiều token nhất
  const contractAddress = "******************************************"; // DatacoinSimpleModule

  const [deployer] = await ethers.getSigners();

  console.log("📋 Transfer Details:");
  console.log("- Network:", hre.network.name);
  console.log("- Chain ID:", (await ethers.provider.getNetwork()).chainId);
  console.log("- Contract Address:", contractAddress);
  console.log("- From Address:", deployer.address);

  // Kết nối với contract
  const DATACOIN = await ethers.getContractFactory("DATACOIN");
  const token = DATACOIN.attach(contractAddress);

  // <PERSON><PERSON><PERSON> tra thông tin token
  const name = await token.name();
  const symbol = await token.symbol();
  const decimals = await token.decimals();

  console.log("- Token Name:", name);
  console.log("- Token Symbol:", symbol);
  console.log("- Token Decimals:", decimals.toString());

  // Kiểm tra balance hiện tại
  const currentBalance = await token.balanceOf(deployer.address);
  console.log("- Current Balance:", ethers.formatEther(currentBalance), symbol);

  if (currentBalance === 0n) {
    console.log("❌ No tokens to transfer!");
    return;
  }

  // Các địa chỉ test để transfer (sử dụng địa chỉ hợp lệ)
  const testAddresses = [
    {
      name: "Test Address 1",
      address: "******************************************",
      amount: "100"
    },
    {
      name: "Test Address 2",
      address: "******************************************",
      amount: "250"
    },
    {
      name: "Test Address 3",
      address: "******************************************",
      amount: "500"
    }
  ];

  console.log("\n🚀 Starting Transfer Tests:");
  console.log("-".repeat(60));

  for (let i = 0; i < testAddresses.length; i++) {
    const testAddr = testAddresses[i];

    console.log(`\n📤 Transfer ${i + 1}: ${testAddr.name}`);
    console.log(`   To: ${testAddr.address}`);
    console.log(`   Amount: ${testAddr.amount} ${symbol}`);

    try {
      // Kiểm tra balance trước transfer
      const balanceBefore = await token.balanceOf(testAddr.address);
      console.log(`   Balance Before: ${ethers.formatEther(balanceBefore)} ${symbol}`);

      // Thực hiện transfer
      const transferAmount = ethers.parseEther(testAddr.amount);
      const tx = await token.transfer(testAddr.address, transferAmount, {
        gasLimit: 100000,
        gasPrice: ethers.parseUnits("20", "gwei")
      });

      console.log(`   🔄 Transaction Hash: ${tx.hash}`);
      console.log(`   ⏳ Waiting for confirmation...`);

      // Chờ transaction được confirm
      const receipt = await tx.wait();

      console.log(`   ✅ Transaction Confirmed!`);
      console.log(`   📦 Block Number: ${receipt.blockNumber}`);
      console.log(`   ⛽ Gas Used: ${receipt.gasUsed.toString()}`);

      // Kiểm tra balance sau transfer
      const balanceAfter = await token.balanceOf(testAddr.address);
      console.log(`   Balance After: ${ethers.formatEther(balanceAfter)} ${symbol}`);

      // Kiểm tra balance của sender
      const senderBalance = await token.balanceOf(deployer.address);
      console.log(`   Sender Balance: ${ethers.formatEther(senderBalance)} ${symbol}`);

      // Thông tin để xem trên explorer
      console.log(`   🔍 Explorer Links:`);
      console.log(`      - Transaction: http://localhost:8545/tx/${tx.hash}`);
      console.log(`      - Block: http://localhost:8545/block/${receipt.blockNumber}`);
      console.log(`      - Address: http://localhost:8545/address/${testAddr.address}`);

      // Delay giữa các transaction
      if (i < testAddresses.length - 1) {
        console.log(`   ⏱️  Waiting 3 seconds before next transfer...`);
        await new Promise(resolve => setTimeout(resolve, 3000));
      }

    } catch (error) {
      console.log(`   ❌ Transfer failed: ${error.message}`);
    }
  }

  // Test transfer ngược lại (từ test address về deployer)
  console.log("\n🔄 Testing Reverse Transfer:");
  console.log("-".repeat(60));

  const reverseTestAddr = testAddresses[0];
  try {
    // Tạo wallet từ private key khác để test
    const testPrivateKey = "0x59c6995e998f97a5a0044966f0945389dc9e86dae88c7a8412f4603b6b78690d";
    const testWallet = new ethers.Wallet(testPrivateKey, ethers.provider);

    console.log(`\n📤 Reverse Transfer Test:`);
    console.log(`   From: ${testWallet.address}`);
    console.log(`   To: ${deployer.address}`);

    // Kiểm tra balance của test wallet
    const testBalance = await token.balanceOf(testWallet.address);
    console.log(`   Test Wallet Balance: ${ethers.formatEther(testBalance)} ${symbol}`);

    if (testBalance > 0) {
      const reverseAmount = ethers.parseEther("10");
      const tokenWithTestWallet = token.connect(testWallet);

      const reverseTx = await tokenWithTestWallet.transfer(deployer.address, reverseAmount, {
        gasLimit: 100000
      });

      console.log(`   🔄 Reverse Transaction Hash: ${reverseTx.hash}`);
      await reverseTx.wait();
      console.log(`   ✅ Reverse transfer successful!`);
    } else {
      console.log(`   ⚠️  Test wallet has no tokens for reverse transfer`);
    }

  } catch (error) {
    console.log(`   ⚠️  Reverse transfer test skipped: ${error.message}`);
  }

  // Tổng kết
  console.log("\n📊 Final Summary:");
  console.log("-".repeat(60));

  const finalBalance = await token.balanceOf(deployer.address);
  const totalSupply = await token.totalSupply();

  console.log(`   Contract: ${contractAddress}`);
  console.log(`   Total Supply: ${ethers.formatEther(totalSupply)} ${symbol}`);
  console.log(`   Deployer Balance: ${ethers.formatEther(finalBalance)} ${symbol}`);

  console.log(`\n   📍 Test Address Balances:`);
  for (const testAddr of testAddresses) {
    const balance = await token.balanceOf(testAddr.address);
    console.log(`      ${testAddr.name}: ${ethers.formatEther(balance)} ${symbol}`);
  }

  // Network info
  const blockNumber = await ethers.provider.getBlockNumber();
  console.log(`\n   🌐 Network Info:`);
  console.log(`      Current Block: ${blockNumber}`);
  console.log(`      RPC URL: http://127.0.0.1:8545`);
  console.log(`      Chain ID: 9000`);

  console.log("\n🎉 Transfer test completed!");
  console.log("💡 Check the transactions on your Ethermint explorer!");
}

main().catch((error) => {
  console.error("💥 Transfer test failed:", error);
  process.exitCode = 1;
});
