async function main() {
  console.log("🔍 Verifying Ignition deployment results...");
  
  // Contract addresses from Ignition deployment
  const contracts = [
    {
      name: "DatacoinDeployModule",
      address: "******************************************"
    },
    {
      name: "DatacoinWithMintModule", 
      address: "******************************************"
    }
  ];
  
  const [deployer] = await ethers.getSigners();
  console.log("Verifying with account:", deployer.address);
  console.log("Network:", hre.network.name);
  console.log("Chain ID:", (await ethers.provider.getNetwork()).chainId);
  
  for (const contract of contracts) {
    console.log(`\n📋 Checking ${contract.name} at ${contract.address}:`);
    
    try {
      // Connect to the contract
      const DATACOIN = await ethers.getContractFactory("DATACOIN");
      const token = DATACOIN.attach(contract.address);
      
      // Get basic info
      const name = await token.name();
      const symbol = await token.symbol();
      const decimals = await token.decimals();
      const totalSupply = await token.totalSupply();
      
      console.log("  ✅ Contract Info:");
      console.log("    - Name:", name);
      console.log("    - Symbol:", symbol);
      console.log("    - Decimals:", decimals.toString());
      console.log("    - Total Supply:", ethers.formatEther(totalSupply));
      
      // Check deployer balance
      const deployerBalance = await token.balanceOf(deployer.address);
      console.log("    - Deployer Balance:", ethers.formatEther(deployerBalance));
      
      // Check test address balance (for WithMint module)
      if (contract.name === "DatacoinWithMintModule") {
        const testAddress = "******************************************";
        const testBalance = await token.balanceOf(testAddress);
        console.log("    - Test Address Balance:", ethers.formatEther(testBalance));
      }
      
      // Check if contract has owner function
      try {
        const owner = await token.owner();
        console.log("    - Owner:", owner);
      } catch (e) {
        console.log("    - Owner: Not available or different function name");
      }
      
      // Test a simple transaction
      console.log("  🧪 Testing transaction...");
      const currentBalance = await token.balanceOf(deployer.address);
      
      if (currentBalance > 0) {
        const transferAmount = ethers.parseEther("1");
        const testRecipient = "0x8ba1f109551bd432803012645hac136c0c8326b1";
        
        const tx = await token.transfer(testRecipient, transferAmount, {
          gasLimit: 100000
        });
        
        console.log("    - Transfer TX:", tx.hash);
        await tx.wait();
        
        const recipientBalance = await token.balanceOf(testRecipient);
        console.log("    - Recipient Balance:", ethers.formatEther(recipientBalance));
        console.log("  ✅ Transaction successful!");
      } else {
        console.log("    - No balance to test transfer");
      }
      
    } catch (error) {
      console.error(`  ❌ Error checking ${contract.name}:`, error.message);
    }
  }
  
  // Network statistics
  console.log("\n📊 Network Statistics:");
  const blockNumber = await ethers.provider.getBlockNumber();
  const gasPrice = await ethers.provider.getFeeData();
  const balance = await ethers.provider.getBalance(deployer.address);
  
  console.log("  - Current Block:", blockNumber);
  console.log("  - Gas Price:", ethers.formatUnits(gasPrice.gasPrice, "gwei"), "gwei");
  console.log("  - Account ETH Balance:", ethers.formatEther(balance));
  
  console.log("\n🎉 Verification completed!");
}

main().catch((error) => {
  console.error("💥 Verification failed:", error);
  process.exitCode = 1;
});
