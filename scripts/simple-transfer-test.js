async function main() {
  console.log("💸 Simple DTC Transfer Test for Explorer");
  console.log("=".repeat(50));
  
  // Contract c<PERSON> nhi<PERSON> token nhất
  const contractAddress = "******************************************";
  
  const [deployer] = await ethers.getSigners();
  
  console.log("📋 Setup:");
  console.log("- Network:", hre.network.name);
  console.log("- Chain ID:", (await ethers.provider.getNetwork()).chainId);
  console.log("- Contract:", contractAddress);
  console.log("- From:", deployer.address);
  
  // Kết nối contract
  const DATACOIN = await ethers.getContractFactory("DATACOIN");
  const token = DATACOIN.attach(contractAddress);
  
  const symbol = await token.symbol();
  const currentBalance = await token.balanceOf(deployer.address);
  console.log("- Balance:", ethers.formatEther(currentBalance), symbol);
  
  // <PERSON><PERSON><PERSON> địa chỉ test hợp lệ
  const recipients = [
    "******************************************",
    "******************************************", 
    "******************************************",
    "******************************************"
  ];
  
  const transferAmounts = ["150", "300", "75", "225"];
  
  console.log("\n🚀 Starting Transfers:");
  console.log("-".repeat(50));
  
  for (let i = 0; i < recipients.length; i++) {
    const recipient = recipients[i];
    const amount = transferAmounts[i];
    
    console.log(`\n📤 Transfer ${i + 1}:`);
    console.log(`   To: ${recipient}`);
    console.log(`   Amount: ${amount} ${symbol}`);
    
    try {
      // Kiểm tra balance trước
      const balanceBefore = await token.balanceOf(recipient);
      console.log(`   Before: ${ethers.formatEther(balanceBefore)} ${symbol}`);
      
      // Transfer
      const transferAmount = ethers.parseEther(amount);
      const tx = await token.transfer(recipient, transferAmount, {
        gasLimit: 100000
      });
      
      console.log(`   TX Hash: ${tx.hash}`);
      
      // Chờ confirm
      const receipt = await tx.wait();
      console.log(`   Block: ${receipt.blockNumber}`);
      console.log(`   Gas: ${receipt.gasUsed.toString()}`);
      
      // Kiểm tra balance sau
      const balanceAfter = await token.balanceOf(recipient);
      console.log(`   After: ${ethers.formatEther(balanceAfter)} ${symbol}`);
      console.log(`   ✅ Success!`);
      
      // Explorer links
      console.log(`   🔍 Explorer:`);
      console.log(`      TX: http://localhost:8545/tx/${tx.hash}`);
      console.log(`      Block: http://localhost:8545/block/${receipt.blockNumber}`);
      
      // Delay
      if (i < recipients.length - 1) {
        console.log(`   ⏱️  Waiting 2 seconds...`);
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
      
    } catch (error) {
      console.log(`   ❌ Failed: ${error.message}`);
    }
  }
  
  // Tạo thêm một vài transaction nữa để có nhiều data trên explorer
  console.log("\n🔄 Additional Transfers:");
  console.log("-".repeat(50));
  
  // Transfer giữa các test addresses
  const additionalTransfers = [
    {
      from: recipients[0],
      to: recipients[1], 
      amount: "25",
      privateKey: "0x59c6995e998f97a5a0044966f0945389dc9e86dae88c7a8412f4603b6b78690d"
    },
    {
      from: recipients[1],
      to: recipients[2],
      amount: "50", 
      privateKey: "0x5de4111afa1a4b94908f83103eb1f1706367c2e68ca870fc3fb9a804cdab365a"
    }
  ];
  
  for (let i = 0; i < additionalTransfers.length; i++) {
    const transfer = additionalTransfers[i];
    
    console.log(`\n📤 Additional Transfer ${i + 1}:`);
    console.log(`   From: ${transfer.from}`);
    console.log(`   To: ${transfer.to}`);
    console.log(`   Amount: ${transfer.amount} ${symbol}`);
    
    try {
      // Tạo wallet từ private key
      const wallet = new ethers.Wallet(transfer.privateKey, ethers.provider);
      
      // Kiểm tra ETH balance cho gas
      const ethBalance = await ethers.provider.getBalance(wallet.address);
      if (ethBalance === 0n) {
        console.log(`   ⚠️  No ETH for gas, skipping...`);
        continue;
      }
      
      // Kiểm tra token balance
      const tokenBalance = await token.balanceOf(wallet.address);
      const transferAmount = ethers.parseEther(transfer.amount);
      
      if (tokenBalance < transferAmount) {
        console.log(`   ⚠️  Insufficient tokens, skipping...`);
        continue;
      }
      
      // Connect contract với wallet
      const tokenWithWallet = token.connect(wallet);
      
      const tx = await tokenWithWallet.transfer(transfer.to, transferAmount, {
        gasLimit: 100000
      });
      
      console.log(`   TX Hash: ${tx.hash}`);
      
      const receipt = await tx.wait();
      console.log(`   Block: ${receipt.blockNumber}`);
      console.log(`   ✅ Success!`);
      
    } catch (error) {
      console.log(`   ⚠️  Skipped: ${error.message}`);
    }
  }
  
  // Final summary
  console.log("\n📊 Final Summary:");
  console.log("-".repeat(50));
  
  const finalBalance = await token.balanceOf(deployer.address);
  console.log(`Deployer Balance: ${ethers.formatEther(finalBalance)} ${symbol}`);
  
  console.log(`\nRecipient Balances:`);
  for (let i = 0; i < recipients.length; i++) {
    const balance = await token.balanceOf(recipients[i]);
    console.log(`  ${recipients[i]}: ${ethers.formatEther(balance)} ${symbol}`);
  }
  
  const blockNumber = await ethers.provider.getBlockNumber();
  console.log(`\nCurrent Block: ${blockNumber}`);
  
  console.log("\n🎉 Transfer test completed!");
  console.log("💡 Check your Ethermint explorer for all transactions!");
  console.log(`🌐 Explorer URL: http://localhost:8545`);
}

main().catch((error) => {
  console.error("💥 Test failed:", error);
  process.exitCode = 1;
});
