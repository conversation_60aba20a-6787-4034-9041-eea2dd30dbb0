async function main() {
  console.log("🪙 Minting tokens and testing full functionality...");

  const [deployer] = await ethers.getSigners();
  const contractAddress = "******************************************";

  console.log("📋 Details:");
  console.log("- Network:", hre.network.name);
  console.log("- Account:", deployer.address);
  console.log("- Contract:", contractAddress);

  // Kết nối với contract
  const DATACOIN = await ethers.getContractFactory("DATACOIN");
  const token = DATACOIN.attach(contractAddress);

  try {
    // Kiểm tra xem có function mint không
    console.log("\n🔍 Checking contract functions...");

    // Kiểm tra balance trước khi mint
    let balance = await token.balanceOf(deployer.address);
    console.log("Balance before mint:", ethers.formatEther(balance));

    // Thử mint token (nếu contract có function mint)
    console.log("\n🪙 Attempting to mint tokens...");
    const mintAmount = ethers.parseEther("1000000"); // 1 triệu token

    // Kiểm tra xem deployer có phải là owner không và có thể mint
    try {
      const mintTx = await token.mint(deployer.address, mintAmount, {
        gasLimit: 200000
      });

      console.log("Mint transaction hash:", mintTx.hash);
      await mintTx.wait();
      console.log("✅ Mint successful!");

      // Kiểm tra balance sau mint
      balance = await token.balanceOf(deployer.address);
      console.log("Balance after mint:", ethers.formatEther(balance));

      const totalSupply = await token.totalSupply();
      console.log("Total supply:", ethers.formatEther(totalSupply));

    } catch (mintError) {
      console.log("⚠️ Mint failed (might not have mint function):", mintError.message);

      // Nếu không mint được, thử transfer từ deployer (nếu có balance)
      balance = await token.balanceOf(deployer.address);
      if (balance > 0) {
        console.log("Using existing balance for testing...");
      } else {
        console.log("No tokens available for testing transfers");
        return;
      }
    }

    // Test transfer nếu có balance
    balance = await token.balanceOf(deployer.address);
    if (balance > 0) {
      console.log("\n💸 Testing token transfer...");
      const transferAmount = ethers.parseEther("100"); // 100 token
      const testAddress = "******************************************".toLowerCase();

      const transferTx = await token.transfer(testAddress, transferAmount, {
        gasLimit: 100000
      });

      console.log("Transfer transaction hash:", transferTx.hash);
      await transferTx.wait();

      const testBalance = await token.balanceOf(testAddress);
      console.log("Test address balance:", ethers.formatEther(testBalance));
      console.log("✅ Transfer successful!");
    }

    // Test approve và transferFrom
    if (balance > ethers.parseEther("200")) {
      console.log("\n🔐 Testing approve and transferFrom...");
      const spender = "******************************************".toLowerCase();
      const approveAmount = ethers.parseEther("50");

      const approveTx = await token.approve(spender, approveAmount, {
        gasLimit: 100000
      });

      console.log("Approve transaction hash:", approveTx.hash);
      await approveTx.wait();

      const allowance = await token.allowance(deployer.address, spender);
      console.log("Allowance:", ethers.formatEther(allowance));
      console.log("✅ Approve successful!");
    }

    // Kiểm tra network stats
    console.log("\n📊 Network Statistics:");
    const blockNumber = await ethers.provider.getBlockNumber();
    const gasPrice = await ethers.provider.getFeeData();
    const network = await ethers.provider.getNetwork();

    console.log("- Block Number:", blockNumber);
    console.log("- Chain ID:", network.chainId.toString());
    console.log("- Gas Price:", ethers.formatUnits(gasPrice.gasPrice, "gwei"), "gwei");

    console.log("\n🎉 All tests completed successfully!");
    console.log("✅ Ethermint connection is fully functional!");

  } catch (error) {
    console.error("❌ Test failed:", error);
    throw error;
  }
}

main().catch((error) => {
  console.error("💥 Script failed:", error);
  process.exitCode = 1;
});
